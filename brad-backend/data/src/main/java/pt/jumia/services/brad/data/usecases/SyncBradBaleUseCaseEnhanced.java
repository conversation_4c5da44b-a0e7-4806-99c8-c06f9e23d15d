package pt.jumia.services.brad.data.usecases;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.ReadExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.UpdateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.fxrate.ReadBradFxRateUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.List;
import java.util.Map;

/**
 * Enhanced data layer implementation of SyncBradBaleUseCase with Spring Batch integration.
 * 
 * This class extends the domain SyncBradBaleUseCase and provides actual Spring Batch
 * job launching capabilities. It resolves the circular dependency issue by implementing
 * the Spring Batch integration in the data layer where Spring Batch dependencies are available.
 * 
 * Key responsibilities:
 * - Extends domain use case with all business logic
 * - Implements actual Spring Batch job launching
 * - Handles job parameter creation and validation
 * - Provides proper error handling and job status reporting
 * - Maintains separation of concerns (domain logic vs infrastructure)
 */
@Slf4j
@Component
public class SyncBradBaleUseCaseEnhanced extends SyncBradBaleUseCase {

    private final JobLauncher jobLauncher;
    private final Job baleSyncJob;

    /**
     * Constructor that properly injects Spring Batch dependencies.
     * 
     * @param bradBaleRepository Repository for Brad bale operations
     * @param readCurrenciesUseCase Use case for reading currencies
     * @param readAccountsUseCase Use case for reading accounts
     * @param updateExecutionLogsUseCase Use case for updating execution logs
     * @param readBradFxRateUseCase Use case for reading FX rates
     * @param readExecutionLogsUseCase Use case for reading execution logs
     * @param readViewEntityUseCase Use case for reading view entities
     * @param jobLauncher Spring Batch job launcher
     * @param baleSyncJob Spring Batch job definition
     */
    public SyncBradBaleUseCaseEnhanced(
            BradBaleRepository bradBaleRepository,
            ReadCurrenciesUseCase readCurrenciesUseCase,
            ReadAccountsUseCase readAccountsUseCase,
            UpdateExecutionLogsUseCase updateExecutionLogsUseCase,
            ReadBradFxRateUseCase readBradFxRateUseCase,
            ReadExecutionLogsUseCase readExecutionLogsUseCase,
            ReadViewEntityUseCase readViewEntityUseCase,
            JobLauncher jobLauncher,
            Job baleSyncJob) {
        
        // Call parent constructor with Object types to avoid circular dependencies
        super(bradBaleRepository, readCurrenciesUseCase, readAccountsUseCase, 
              updateExecutionLogsUseCase, readBradFxRateUseCase, readExecutionLogsUseCase,
              readViewEntityUseCase, jobLauncher, baleSyncJob);
        
        // Store actual Spring Batch dependencies
        this.jobLauncher = jobLauncher;
        this.baleSyncJob = baleSyncJob;
    }

    /**
     * Implements actual Spring Batch job launching with proper parameter handling.
     * 
     * This method overrides the placeholder implementation in the domain class
     * and provides real Spring Batch job execution capabilities.
     * 
     * @param parameters Job parameters map containing execution context
     * @return JobExecution result with job status and execution details
     * @throws Exception if job launch fails
     */
    @Override
    protected Object launchBatchJob(Map<String, Object> parameters) throws Exception {
        log.info("Launching Spring Batch job with parameters: {}", parameters);
        
        try {
            // Build JobParameters from the provided map
            JobParametersBuilder builder = new JobParametersBuilder();
            
            // Add standard parameters
            for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                
                if (value instanceof String) {
                    builder.addString(key, (String) value);
                } else if (value instanceof Long) {
                    builder.addLong(key, (Long) value);
                } else if (value instanceof Integer) {
                    builder.addLong(key, ((Integer) value).longValue());
                } else if (value instanceof Double) {
                    builder.addDouble(key, (Double) value);
                } else {
                    // Convert other types to string
                    builder.addString(key, String.valueOf(value));
                }
            }
            
            JobParameters jobParameters = builder.toJobParameters();
            
            // Launch the job
            JobExecution jobExecution = jobLauncher.run(baleSyncJob, jobParameters);
            
            log.info("Spring Batch job launched successfully. Job ID: {}, Status: {}", 
                    jobExecution.getJobId(), jobExecution.getStatus());
            
            return jobExecution;
            
        } catch (Exception e) {
            log.error("Failed to launch Spring Batch job with parameters: {}", parameters, e);
            throw new RuntimeException("Spring Batch job launch failed: " + e.getMessage(), e);
        }
    }

    /**
     * Enhanced trigger method that supports filtering by specific parameters.
     * 
     * This method extends the base functionality to support more complex job parameter
     * scenarios that may be needed for manual sync operations.
     * 
     * @param entryNo Starting entry number for synchronization
     * @param baleViewEntityId Optional view entity ID for filtering
     * @param additionalParams Additional job parameters
     * @return JobExecution result
     * @throws Exception if job launch fails
     */
    public JobExecution triggerBaleSyncWithParams(Integer entryNo, Long baleViewEntityId, 
                                                  Map<String, Object> additionalParams) throws Exception {
        log.info("Triggering enhanced bale sync with entryNo: {}, viewEntityId: {}, additional params: {}", 
                entryNo, baleViewEntityId, additionalParams);
        
        try {
            // Create comprehensive job parameters
            JobParametersBuilder builder = new JobParametersBuilder();
            
            // Add core parameters
            builder.addLong("timestamp", System.currentTimeMillis());
            builder.addString("triggeredBy", "enhanced-manual-sync");
            
            if (entryNo != null) {
                builder.addLong("entryNo", entryNo.longValue());
            }
            
            if (baleViewEntityId != null) {
                builder.addLong("baleViewEntityId", baleViewEntityId);
            }
            
            // Add any additional parameters
            if (additionalParams != null) {
                for (Map.Entry<String, Object> entry : additionalParams.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    
                    if (value instanceof String) {
                        builder.addString(key, (String) value);
                    } else if (value instanceof Long) {
                        builder.addLong(key, (Long) value);
                    } else if (value instanceof Integer) {
                        builder.addLong(key, ((Integer) value).longValue());
                    } else if (value instanceof Double) {
                        builder.addDouble(key, (Double) value);
                    } else {
                        builder.addString(key, String.valueOf(value));
                    }
                }
            }
            
            JobParameters jobParameters = builder.toJobParameters();
            
            // Launch the job
            JobExecution jobExecution = jobLauncher.run(baleSyncJob, jobParameters);
            
            log.info("Enhanced bale sync job launched successfully. Job ID: {}, Status: {}", 
                    jobExecution.getJobId(), jobExecution.getStatus());
            
            return jobExecution;
            
        } catch (Exception e) {
            log.error("Failed to launch enhanced bale sync job", e);
            throw new RuntimeException("Enhanced bale sync job launch failed: " + e.getMessage(), e);
        }
    }

    /**
     * Convenience method for triggering sync with view entity list.
     * 
     * @param baleViewIds List of view entity IDs to process
     * @return JobExecution result
     * @throws Exception if job launch fails
     */
    public JobExecution triggerBaleSyncForViewList(List<Integer> baleViewIds) throws Exception {
        log.info("Triggering bale sync for view list: {}", baleViewIds);
        
        if (baleViewIds == null || baleViewIds.isEmpty()) {
            throw new IllegalArgumentException("Bale view IDs list cannot be null or empty");
        }
        
        // Convert list to comma-separated string for job parameter
        String viewIdsParam = baleViewIds.stream()
                .map(String::valueOf)
                .reduce((a, b) -> a + "," + b)
                .orElse("");
        
        Map<String, Object> params = Map.of(
                "baleViewIds", viewIdsParam,
                "viewCount", baleViewIds.size()
        );
        
        return triggerBaleSyncWithParams(null, null, params);
    }
}
