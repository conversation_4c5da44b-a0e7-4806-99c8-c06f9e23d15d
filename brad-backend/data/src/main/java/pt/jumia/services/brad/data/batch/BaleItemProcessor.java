package pt.jumia.services.brad.data.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;

/**
 * Spring Batch ItemProcessor for Bale entities.
 * 
 * This component handles the business logic for processing individual Bale items.
 * It replaces the complex error handling and batch processing logic with simple,
 * focused item-by-item processing.
 * 
 * Key simplifications:
 * - Processes one bale at a time (Spring Batch handles chunking)
 * - Returns null for invalid items (Spring Batch will skip them)
 * - Clear, focused responsibility
 * - No complex error aggregation or memory management
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaleItemProcessor implements ItemProcessor<Bale, Bale> {

    private final SyncBradBaleUseCase syncBradBaleUseCase;

    @Override
    public Bale process(Bale bale) throws Exception {
        if (bale == null) {
            return null;
        }

        try {
            log.debug("Processing bale with entry number: {}", bale.getEntryNo());

            // Delegate to consolidated use case
            Bale enrichedBale = syncBradBaleUseCase.processBale(bale);

            log.debug("Successfully processed bale with entry number: {}", bale.getEntryNo());
            return enrichedBale;

        } catch (Exception e) {
            log.warn("Skipping invalid bale with entry number: {} - {}", bale.getEntryNo(), e.getMessage());
            // Return null to skip this item (Spring Batch will handle the skip)
            return null;
        }
    }
}
