package pt.jumia.services.brad.data.batch;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;

import java.util.List;

/**
 * Spring Batch ItemWriter for Bale entities.
 * 
 * This component handles writing processed Bale entities to the Brad database.
 * It replaces the complex custom writing logic with a simple, focused writer.
 * 
 * Key simplifications:
 * - Uses existing BradBaleRepository.sync() method
 * - No complex error handling (delegated to Spring Batch)
 * - Clear, single responsibility
 * - Leverages Spring Batch's chunk-based transaction management
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaleItemWriter implements ItemWriter<Bale> {

    private final SyncBradBaleUseCase syncBradBaleUseCase;

    @Override
    public void write(Chunk<? extends Bale> chunk) throws Exception {
        List<? extends Bale> bales = chunk.getItems();
        
        if (bales.isEmpty()) {
            log.debug("No bales to write in this chunk");
            return;
        }

        log.info("Writing chunk of {} bales to Brad database", bales.size());
        
        try {
            // Delegate to consolidated use case
            List<Bale> syncedBales = syncBradBaleUseCase.execute((List<Bale>) bales);

            log.info("Successfully synced {} bales to Brad database", syncedBales.size());

            if (syncedBales.size() != bales.size()) {
                log.warn("Expected to sync {} bales but only {} were synced",
                        bales.size(), syncedBales.size());
            }

        } catch (Exception e) {
            log.error("Failed to write chunk of {} bales to Brad database", bales.size(), e);
            throw e; // Let Spring Batch handle the error (retry, skip, etc.)
        }
    }
}
