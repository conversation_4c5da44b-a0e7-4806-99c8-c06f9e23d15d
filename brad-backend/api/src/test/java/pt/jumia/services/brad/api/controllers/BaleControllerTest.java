package pt.jumia.services.brad.api.controllers;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.filter.PageFilters;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleFilters;
import pt.jumia.services.brad.domain.entities.filter.bale.BaleSortFilters;
import pt.jumia.services.brad.domain.enumerations.OrderDirection;
import pt.jumia.services.brad.domain.exceptions.UserForbiddenException;
import pt.jumia.services.brad.domain.usecases.acl.ValidateUserAccessUseCase;
import pt.jumia.services.brad.domain.usecases.bale.ReadBaleUseCase;
import pt.jumia.services.brad.domain.usecases.bale.brad.ReadBradBaleUseCase;
import pt.jumia.services.brad.domain.usecases.bale.brad.SyncBradBaleUseCase;

import java.util.List;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(SpringExtension.class)
@WebMvcTest(BaleController.class)
public class BaleControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ValidateUserAccessUseCase validateUserAccessUseCase;

    @MockBean
    private ReadBradBaleUseCase readBradBaleUseCase;

    @MockBean
    private ReadBaleUseCase readBaleUseCase;

    @MockBean
    private SyncBradBaleUseCase syncBradBaleUseCase;

    @BeforeEach
    public void setup() throws UserForbiddenException {
        RequestUser requestUser = new RequestUser();
        doNothing().when(validateUserAccessUseCase).checkCanAccess(requestUser);
    }


    @Test
    public void testFetch() throws Exception {
        List<Bale> baleList = FakeBale.getFakeBale(5);
        PageFilters pageFilters = new PageFilters();
        pageFilters.setPage(1);
        pageFilters.setSize(5);

        BaleSortFilters baleSortFilters = new BaleSortFilters(Bale.SortingFields.ENTRY_NO, OrderDirection.DESC);

        BaleFilters baleFilters = BaleFilters.builder().build();

        when(readBradBaleUseCase.execute(baleFilters, baleSortFilters, pageFilters)).thenReturn(baleList);
        when(readBradBaleUseCase.executeCount(baleFilters)).thenReturn(5L);

        mockMvc
                .perform(get("/api/bales"))
                .andExpect(status().isOk())
                .andReturn();

    }
}
