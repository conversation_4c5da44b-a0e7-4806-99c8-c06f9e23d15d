package pt.jumia.services.brad.domain.usecases.bale;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeViewEntity;
import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Simplified test for ReadBaleUseCase after Spring Batch migration.
 *
 * This test class has been updated to reflect the simplified ReadBaleUseCase
 * that now focuses only on read-only operations. All sync operations have been
 * moved to Spring Batch components.
 */
@ExtendWith(MockitoExtension.class)
public class ReadBaleUseCaseTest {

    private static final List<Bale> BALE_LIST = FakeBale.getFakeBale(10);

    @Mock
    private BaleRepository baleRepository;

    @Mock
    private ReadViewEntityUseCase readViewEntityUseCase;

    @InjectMocks
    private ReadBaleUseCase readBaleUseCase;

    @Test
    public void executeLastBaleOfOffset_success() throws EntityErrorsException, NotFoundException {
        // Test the remaining read-only functionality
        Long baleViewEntityId = 1L;
        Integer offset = 10;
        Integer expectedResult = 12345;
        ViewEntity viewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);

        when(readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE))
                .thenReturn(viewEntity);
        when(baleRepository.findLastBaleWithOffset(viewEntity, offset))
                .thenReturn(expectedResult);

        Integer result = readBaleUseCase.executeLastBaleOfOffset(baleViewEntityId, offset);

        assertEquals(expectedResult, result);
        verify(readViewEntityUseCase).execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        verify(baleRepository).findLastBaleWithOffset(viewEntity, offset);
    }

    @Test
    public void executeAllLastBaleOfOffset_success() throws EntityErrorsException, NotFoundException {
        // Test the second remaining read-only functionality
        Long baleViewEntityId = 1L;
        Integer offset = 5;
        List<Bale> expectedBales = BALE_LIST.subList(0, 3);
        ViewEntity viewEntity = FakeViewEntity.getFakeViewEntity(1).get(0);

        when(readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE))
                .thenReturn(viewEntity);
        when(baleRepository.findAllLastBaleWithOffset(viewEntity, offset))
                .thenReturn(expectedBales);

        List<Bale> result = readBaleUseCase.executeAllLastBaleOfOffset(baleViewEntityId, offset);

        assertEquals(expectedBales, result);
        verify(readViewEntityUseCase).execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        verify(baleRepository).findAllLastBaleWithOffset(viewEntity, offset);
    }

    @Test
    public void executeLastBaleOfOffset_whenViewEntityNotFound_throwsNotFoundException() throws EntityErrorsException {
        // Test error handling
        Long baleViewEntityId = 999L;
        Integer offset = 10;

        when(readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE))
                .thenThrow(new NotFoundException("View entity not found"));

        assertThrows(NotFoundException.class, () -> {
            readBaleUseCase.executeLastBaleOfOffset(baleViewEntityId, offset);
        });

        verify(readViewEntityUseCase).execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        verify(baleRepository, never()).findLastBaleWithOffset(any(), any());
    }

    @Test
    public void executeAllLastBaleOfOffset_whenViewEntityNotFound_throwsNotFoundException() throws EntityErrorsException {
        // Test error handling for the second method
        Long baleViewEntityId = 999L;
        Integer offset = 5;

        when(readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE))
                .thenThrow(new NotFoundException("View entity not found"));

        assertThrows(NotFoundException.class, () -> {
            readBaleUseCase.executeAllLastBaleOfOffset(baleViewEntityId, offset);
        });

        verify(readViewEntityUseCase).execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        verify(baleRepository, never()).findAllLastBaleWithOffset(any(), any());
    }

    @Test
    void simplifiedReadBaleUseCase_HasCorrectDependencies_ConfirmsSimplification() {
        // This test verifies that the simplified ReadBaleUseCase has the correct minimal dependencies
        assertNotNull(readBaleUseCase);

        // Verify that the simplified constructor works
        assertDoesNotThrow(() -> {
            ReadBaleUseCase testInstance = new ReadBaleUseCase(baleRepository, readViewEntityUseCase);
            assertNotNull(testInstance);
        });
    }

    @Test
    void simplifiedReadBaleUseCase_OnlyHasReadMethods_ConfirmsCleanup() {
        // This test verifies that only read-only methods remain
        String className = readBaleUseCase.getClass().getSimpleName();
        assertEquals("ReadBaleUseCase", className);

        // Verify the remaining read methods exist
        assertDoesNotThrow(() -> {
            readBaleUseCase.getClass().getMethod("executeLastBaleOfOffset", Long.class, Integer.class);
            readBaleUseCase.getClass().getMethod("executeAllLastBaleOfOffset", Long.class, Integer.class);
        });
    }
}
