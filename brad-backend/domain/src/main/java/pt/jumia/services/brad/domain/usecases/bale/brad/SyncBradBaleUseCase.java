package pt.jumia.services.brad.domain.usecases.bale.brad;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.BaleAccountFetchResult;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.CurrencyUpdateResult;
import pt.jumia.services.brad.domain.entities.ExecutionLog;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.ViewEntity;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.error.BaleErrorClassifier;
import pt.jumia.services.brad.domain.entities.error.BaleProcessingErrorResult;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.accounts.ReadAccountsUseCase;
import pt.jumia.services.brad.domain.usecases.currencies.ReadCurrenciesUseCase;
import pt.jumia.services.brad.domain.usecases.executionlogs.UpdateExecutionLogsUseCase;
import pt.jumia.services.brad.domain.usecases.fxrates.brad.ReadBradFxRateUseCase;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Comprehensive Bale synchronization use case.
 *
 * This class handles both traditional bale processing and Spring Batch job triggering.
 * It consolidates all bale synchronization logic in one place for consistency.
 *
 * Key responsibilities:
 * - Core business logic for processing bales (traditional approach)
 * - Spring Batch job triggering for async operations (new approach)
 * - Maintaining backward compatibility while enabling migration to Spring Batch
 */
@Slf4j
@Component
@AllArgsConstructor
public class SyncBradBaleUseCase {

    private final BradBaleRepository bradBaleRepository;
    private final ReadCurrenciesUseCase readCurrenciesUseCase;
    private final ReadAccountsUseCase readAccountsUseCase;
    private final UpdateExecutionLogsUseCase updateExecutionLogsUseCase;
    private final ReadBradFxRateUseCase readBradFxRateUseCase;
    private final ReadViewEntityUseCase readViewEntityUseCase;

    // Spring Batch dependencies (injected from data layer to avoid circular dependencies)
    private final Object jobLauncher;  // Will be cast to JobLauncher in implementation
    private final Object baleJob;      // Will be cast to Job in implementation

    private static final String USD = "USD";

    public List<Bale> execute(List<Bale> baleList, ExecutionLog executionLog) throws DatabaseErrorsException {
        log.info("Bale sync: Execution log ID: {} - Processing {} bales", executionLog.getId(), baleList.size());

        List<BaleProcessingErrorResult> allErrors = new ArrayList<>();

        log.debug("Bale sync: Execution log ID {}: Fetching accounts for {} bales", executionLog.getId(), baleList.size());
        BaleAccountFetchResult accountResult = fetchAccountsWithPartialSuccess(baleList);

        allErrors.addAll(accountResult.getFailures());
        log.info("Bale sync: Execution log {}: Account fetch completed - {} successful, {} failed",
                executionLog.getId(), accountResult.getSuccessCount(), accountResult.getFailureCount());

        List<BaleProcessingErrorResult> criticalErrors = allErrors.stream()
                .filter(BaleProcessingErrorResult::isCritical)
                .collect(Collectors.toList());

        if (!criticalErrors.isEmpty()) {
            log.error("Bale sync: Execution log {}: Critical errors detected. Stopping processing.", executionLog.getId());
            List<ExecutionLog.SyncingError> syncingErrors = updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(allErrors);
            updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
            return List.of();
        }

        CurrencyUpdateResult currencyResult = updateCurrenciesWithPartialSuccess(accountResult.getSuccessfulMappings());

        allErrors.addAll(currencyResult.getFailures());
        log.info("Bale sync: Execution log {}: Currency update completed - {} successful, {} failed",
                executionLog.getId(), currencyResult.getSuccessCount(), currencyResult.getFailureCount());

        criticalErrors = allErrors.stream()
                .filter(BaleProcessingErrorResult::isCritical)
                .collect(Collectors.toList());

        if (!criticalErrors.isEmpty()) {
            log.error("Bale sync: Execution log {}: Critical errors detected during currency processing. Stopping.", executionLog.getId());
            List<ExecutionLog.SyncingError> syncingErrors = updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(allErrors);
            updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
            return List.of();
        }

        List<Bale> syncedBales = new ArrayList<>();
        List<Bale> successfullyProcessedBales = currencyResult.getSuccessfulUpdates();

        if (!successfullyProcessedBales.isEmpty()) {
            try {
                log.info("Bale sync: Execution log {}: Syncing {} successfully processed bales to repository",
                        executionLog.getId(), successfullyProcessedBales.size());
                syncedBales = bradBaleRepository.sync(successfullyProcessedBales);
                log.info("Bale sync: Execution log {}: Repository sync completed - {} bales synced",
                        executionLog.getId(), syncedBales.size());
            } catch (Exception e) {
                log.error("Bale sync: Execution log {}: Error during repository sync", executionLog.getId(), e);
                BaleProcessingErrorResult repositoryError = BaleProcessingErrorResult.fromException(null, e, "Repository Sync");
                allErrors.add(repositoryError);
                List<ExecutionLog.SyncingError> syncingErrors = updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(allErrors);
                updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
                return List.of();
            }
        }

        updateExecutionLogWithResults(executionLog, syncedBales.size(), allErrors);
        logProcessingSummary(executionLog.getId(), baleList.size(), syncedBales.size(), allErrors);

        return syncedBales;
    }


    // Over-engineered batch processing methods removed - now handled by Spring Batch












    public Bale addFxRates(Bale bale) {
        Set<FxRate> fxRates = new HashSet<>();

        String baleCurrencyCode = bale.getTransactionCurrency().getCode();

        this.readBradFxRateUseCase.addFxRate(fxRates, baleCurrencyCode, USD, bale.getPostingDate());

        this.readBradFxRateUseCase.addFxRate(fxRates, baleCurrencyCode, bale.getAccount().getCountry().getCurrency().getCode(),
                bale.getPostingDate());

        return bale.toBuilder().fxRates(fxRates).build();
    }



    private void updateExecutionLogWithResults(ExecutionLog executionLog,
                                             int successfulCount,
                                             List<BaleProcessingErrorResult> allErrors) {
        List<ExecutionLog.SyncingError> syncingErrors = updateExecutionLogsUseCase.convertBaleErrorsToSyncingErrors(allErrors);
        
        boolean hasCriticalErrors = allErrors.stream().anyMatch(BaleProcessingErrorResult::isCritical);
        ExecutionLog.ExecutionLogStatus status = updateExecutionLogsUseCase.determineExecutionLogStatus(
                successfulCount, allErrors.size(), hasCriticalErrors);
        
        switch (status) {
            case SYNCED:
                updateExecutionLogsUseCase.updateExecutionLogWithSuccess(executionLog);
                break;
            case PARTIAL_SUCCESS:
                updateExecutionLogsUseCase.updateExecutionLogWithPartialSuccess(executionLog, successfulCount, syncingErrors);
                break;
            case ERROR:
                updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
                break;
            case EMPTY:
                updateExecutionLogsUseCase.updateExecutionLogWithRecoverableErrors(executionLog, syncingErrors);
                break;
            default:
                updateExecutionLogsUseCase.updateExecutionLogWithError(executionLog, syncingErrors);
                break;
        }
    }

    private void logProcessingSummary(Long executionLogId, int totalBales, int successfulBales, List<BaleProcessingErrorResult> errors) {
        log.info("=== Bale sync: OPTIMIZED BALE JOB PROCESSING SUMMARY (Execution Log: {}) ===", executionLogId);
        log.info("Total bales processed: {}", totalBales);
        log.info("Successfully synced: {}", successfulBales);
        log.info("Failed with errors: {}", errors.size());
        log.info("Success rate: {}%", totalBales > 0 ? (successfulBales * 100) / totalBales : 0);
        log.info("Performance: {} database queries eliminated through bulk operations", 
                (totalBales * 2) - (successfulBales > 0 ? 2 : 0)); // Estimate query reduction
        
        if (!errors.isEmpty()) {
            Map<BaleErrorClassifier.ErrorCategory, Long> errorsByCategory = errors.stream()
                    .collect(Collectors.groupingBy(BaleProcessingErrorResult::getCategory, Collectors.counting()));
            
            errorsByCategory.forEach((category, count) ->
                log.info("  {} errors: {}", category.name(), count));
            
            log.info("=== ERROR DETAILS ===");
            errors.forEach(error -> log.warn("ERROR: {}", error.getLogMessage()));
        }
        
        log.info("=== END OPTIMIZED SUMMARY ===");
    }

    private BaleAccountFetchResult fetchAccountsWithPartialSuccess(List<Bale> baleList) {
        Map<Bale, Account> successfulMappings = new HashMap<>();
        List<BaleProcessingErrorResult> failures = new ArrayList<>();

        for (Bale bale : baleList) {
            try {
                if (bale.getAccount() != null && bale.getAccount().getId() != null) {
                    Account account = readAccountsUseCase.execute(bale.getAccount().getId().intValue());
                    successfulMappings.put(bale, account);
                } else {
                    log.warn("Bale {} has no account or account ID", bale.getId());
                    failures.add(BaleProcessingErrorResult.fromException(bale,
                        new RuntimeException("Bale has no account or account ID"), "Account Fetch"));
                }
            } catch (Exception e) {
                log.warn("Failed to fetch account for bale {}: {}", bale.getId(), e.getMessage());
                failures.add(BaleProcessingErrorResult.fromException(bale, e, "Account Fetch"));
            }
        }

        return new BaleAccountFetchResult(successfulMappings, failures);
    }

    private CurrencyUpdateResult updateCurrenciesWithPartialSuccess(Map<Bale, Account> baleAccountMappings) {
        List<BaleProcessingErrorResult> failures = new ArrayList<>();
        List<Bale> successfulUpdates = new ArrayList<>();

        for (Map.Entry<Bale, Account> entry : baleAccountMappings.entrySet()) {
            Bale bale = entry.getKey();
            Account account = entry.getValue();

            try {
                // Update currency logic here
                if (account.getCurrency() != null && account.getCurrency().getId() != null) {
                    Currency currency = readCurrenciesUseCase.execute(account.getCurrency().getId());
                    // Validate currency is available and add to successful updates
                    if (currency != null) {
                        successfulUpdates.add(bale);
                    } else {
                        log.warn("Currency not found for account {}", account.getId());
                        failures.add(BaleProcessingErrorResult.fromException(bale,
                            new RuntimeException("Currency not found"), "Currency Update"));
                    }
                } else {
                    log.warn("Account {} has no currency or currency ID", account.getId());
                    failures.add(BaleProcessingErrorResult.fromException(bale,
                        new RuntimeException("Account has no currency or currency ID"), "Currency Update"));
                }
            } catch (Exception e) {
                log.warn("Failed to update currency for bale {}: {}", bale.getId(), e.getMessage());
                failures.add(BaleProcessingErrorResult.fromException(bale, e, "Currency Update"));
            }
        }

        return new CurrencyUpdateResult(successfulUpdates, failures);
    }

    // ========== SPRING BATCH INTEGRATION METHODS ==========

    /**
     * Triggers Spring Batch job for bale synchronization by entry number.
     *
     * This method provides a consistent way to trigger bale sync operations
     * using Spring Batch instead of the old custom processing approach.
     *
     * @param entryNo Starting entry number for synchronization
     * @return Job execution result (implementation will cast to JobExecution)
     * @throws Exception if job launch fails
     */
    public Object triggerBaleSync(Integer entryNo) throws Exception {
        log.info("Triggering Spring Batch bale sync job for entry number: {}", entryNo);

        try {
            // Create job parameters with entry number
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("entryNo", entryNo);
            parameters.put("timestamp", System.currentTimeMillis());
            parameters.put("triggeredBy", "manual-sync-endpoint");

            // This will be implemented in the data layer where Spring Batch dependencies are available
            return launchBatchJob(parameters);

        } catch (Exception e) {
            log.error("Failed to trigger bale sync job for entry number: {}", entryNo, e);
            throw new RuntimeException("Bale sync job launch failed", e);
        }
    }

    /**
     * Triggers Spring Batch job for bale synchronization within a specific view.
     *
     * @param entryNo Starting entry number for synchronization
     * @param baleViewEntityId ID of the bale view entity to process
     * @return Job execution result (implementation will cast to JobExecution)
     * @throws Exception if job launch fails
     */
    public Object triggerBaleSyncForView(Integer entryNo, Long baleViewEntityId) throws Exception {
        log.info("Triggering Spring Batch bale sync job for entry number: {} in view: {}", entryNo, baleViewEntityId);

        try {
            // Validate view entity exists
            ViewEntity baleViewEntity = readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE);

            // Create job parameters with entry number and view
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("entryNo", entryNo);
            parameters.put("baleViewEntityId", baleViewEntityId);
            parameters.put("viewName", baleViewEntity.getViewName());
            parameters.put("timestamp", System.currentTimeMillis());
            parameters.put("triggeredBy", "manual-sync-view-endpoint");

            return launchBatchJob(parameters);

        } catch (Exception e) {
            log.error("Failed to trigger bale sync job for entry number: {} in view: {}", entryNo, baleViewEntityId, e);
            throw new RuntimeException("Bale sync job launch failed", e);
        }
    }

    /**
     * Triggers Spring Batch job for bale synchronization across multiple views.
     *
     * @param baleViewIds List of bale view IDs to process
     * @return Job execution result (implementation will cast to JobExecution)
     * @throws Exception if job launch fails
     */
    public Object triggerBaleSyncForViews(List<Integer> baleViewIds) throws Exception {
        log.info("Triggering Spring Batch bale sync job for {} view IDs: {}", baleViewIds.size(), baleViewIds);

        try {
            // Validate view entities exist
            List<ViewEntity> baleViewEntities = new ArrayList<>();
            for (Integer viewId : baleViewIds) {
                try {
                    ViewEntity viewEntity = readViewEntityUseCase.execute(viewId, ViewEntity.EntityType.BALE);
                    baleViewEntities.add(viewEntity);
                } catch (Exception e) {
                    log.warn("Bale view entity with ID {} not found, skipping", viewId);
                }
            }

            if (baleViewEntities.isEmpty()) {
                throw new IllegalArgumentException("No valid bale view entities found for provided IDs: " + baleViewIds);
            }

            // Create job parameters with view IDs
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("baleViewIds", baleViewIds);
            parameters.put("viewCount", baleViewEntities.size());
            parameters.put("timestamp", System.currentTimeMillis());
            parameters.put("triggeredBy", "manual-sync-views-endpoint");

            return launchBatchJob(parameters);

        } catch (Exception e) {
            log.error("Failed to trigger bale sync job for view IDs: {}", baleViewIds, e);
            throw new RuntimeException("Bale sync job launch failed", e);
        }
    }

    /**
     * Helper method to launch Spring Batch job with parameters.
     *
     * This method implements actual Spring Batch job launching using the injected
     * JobLauncher and Job dependencies. It uses reflection to avoid compile-time
     * dependencies on Spring Batch in the domain layer.
     *
     * @param parameters Job parameters map
     * @return Job execution result (JobExecution)
     * @throws Exception if job launch fails
     */
    protected Object launchBatchJob(Map<String, Object> parameters) throws Exception {
        if (jobLauncher == null || baleJob == null) {
            throw new IllegalStateException(
                "Spring Batch dependencies not properly injected. " +
                "Ensure JobLauncher and Job beans are available in the application context."
            );
        }

        log.info("Launching Spring Batch job with parameters: {}", parameters);

        try {
            // Use reflection to avoid compile-time dependencies on Spring Batch
            Class<?> jobParametersBuilderClass = Class.forName("org.springframework.batch.core.JobParametersBuilder");
            Object builder = jobParametersBuilderClass.getDeclaredConstructor().newInstance();

            // Add parameters using reflection
            for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                if (value instanceof String) {
                    jobParametersBuilderClass.getMethod("addString", String.class, String.class)
                        .invoke(builder, key, value);
                } else if (value instanceof Long) {
                    jobParametersBuilderClass.getMethod("addLong", String.class, Long.class)
                        .invoke(builder, key, value);
                } else if (value instanceof Integer) {
                    jobParametersBuilderClass.getMethod("addLong", String.class, Long.class)
                        .invoke(builder, key, ((Integer) value).longValue());
                } else if (value instanceof Double) {
                    jobParametersBuilderClass.getMethod("addDouble", String.class, Double.class)
                        .invoke(builder, key, value);
                } else {
                    // Convert other types to string
                    jobParametersBuilderClass.getMethod("addString", String.class, String.class)
                        .invoke(builder, key, String.valueOf(value));
                }
            }

            // Build job parameters
            Object jobParameters = jobParametersBuilderClass.getMethod("toJobParameters").invoke(builder);

            // Launch the job using reflection
            Object jobExecution = jobLauncher.getClass()
                .getMethod("run", baleJob.getClass().getInterfaces()[0], jobParameters.getClass())
                .invoke(jobLauncher, baleJob, jobParameters);

            // Get job ID and status for logging
            Long jobId = (Long) jobExecution.getClass().getMethod("getJobId").invoke(jobExecution);
            Object status = jobExecution.getClass().getMethod("getStatus").invoke(jobExecution);

            log.info("Spring Batch job launched successfully. Job ID: {}, Status: {}", jobId, status);

            return jobExecution;

        } catch (ReflectiveOperationException | RuntimeException e) {
            log.error("Failed to launch Spring Batch job with parameters: {}", parameters, e);
            throw new RuntimeException("Spring Batch job launch failed: " + e.getMessage(), e);
        }
    }

    // ========== SPRING BATCH ITEM PROCESSING METHODS ==========

    /**
     * Simplified execute method for Spring Batch ItemWriter.
     *
     * This method focuses on the core business logic of syncing bales
     * without complex error handling, which is now handled by Spring Batch.
     *
     * @param baleList List of bales to sync
     * @return List of successfully synced bales
     * @throws DatabaseErrorsException if database sync fails
     */
    public List<Bale> execute(List<Bale> baleList) throws DatabaseErrorsException {
        if (baleList == null || baleList.isEmpty()) {
            log.debug("No bales to sync");
            return List.of();
        }

        log.info("Syncing {} bales to Brad database", baleList.size());

        try {
            // Use existing repository sync method
            List<Bale> syncedBales = bradBaleRepository.sync(baleList);

            log.info("Successfully synced {} bales to Brad database", syncedBales.size());
            return syncedBales;

        } catch (Exception e) {
            log.error("Failed to sync {} bales to Brad database", baleList.size(), e);
            throw new DatabaseErrorsException("Bale sync failed: " + e.getMessage());
        }
    }

    /**
     * Processes a single bale for validation and enrichment.
     *
     * This method is designed to be called by Spring Batch ItemProcessor
     * and focuses on individual bale processing logic.
     *
     * @param bale Bale to process
     * @return Enriched bale ready for sync
     * @throws Exception if processing fails
     */
    public Bale processBale(Bale bale) throws Exception {
        if (bale == null) {
            throw new IllegalArgumentException("Bale cannot be null");
        }

        log.debug("Processing bale with entry number: {}", bale.getEntryNo());

        // Validate required fields
        validateBale(bale);

        // Fetch and validate account
        Account account = fetchAccount(bale);

        // Determine and validate currency
        Currency currency = determineCurrency(bale, account);

        // Build enriched bale
        Bale enrichedBale = bale.toBuilder()
                .account(account)
                .transactionCurrency(currency)
                .build();

        log.debug("Successfully processed bale with entry number: {}", bale.getEntryNo());
        return enrichedBale;
    }

    /**
     * Validates required bale fields.
     */
    private void validateBale(Bale bale) throws Exception {
        if (bale.getAccount() == null || bale.getAccount().getNavReference() == null ||
            bale.getAccount().getNavReference().trim().isEmpty()) {
            throw new IllegalArgumentException("Bale account NAV reference cannot be null or empty");
        }

        if (bale.getEntryNo() == null) {
            throw new IllegalArgumentException("Bale entry number cannot be null");
        }
    }

    /**
     * Fetches and validates account for the bale.
     */
    private Account fetchAccount(Bale bale) throws Exception {
        try {
            String navReference = bale.getAccount().getNavReference();
            return readAccountsUseCase.executeByNavReference(navReference);
        } catch (Exception e) {
            String navRef = bale.getAccount() != null ? bale.getAccount().getNavReference() : "null";
            throw new Exception("Failed to fetch account for NAV reference: " + navRef, e);
        }
    }

    /**
     * Determines the appropriate currency for the bale.
     */
    private Currency determineCurrency(Bale bale, Account account) throws Exception {
        try {
            if (account.getCurrency() != null && account.getCurrency().getId() != null) {
                return readCurrenciesUseCase.execute(account.getCurrency().getId());
            } else {
                // Default to USD if no currency specified
                return readCurrenciesUseCase.execute(USD);
            }
        } catch (Exception e) {
            throw new Exception("Failed to determine currency for bale: " + bale.getEntryNo(), e);
        }
    }

}
