package pt.jumia.services.brad.domain.usecases.bale;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.ViewEntity;

import pt.jumia.services.brad.domain.exceptions.EntityErrorsException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.BaleRepository;
import pt.jumia.services.brad.domain.usecases.viewentity.ReadViewEntityUseCase;

import java.util.List;

/**
 * Simplified <PERSON>le read use case focused on read-only operations.
 *
 * This class has been simplified as part of the Spring Batch migration.
 * All sync operations have been moved to Spring Batch components.
 *
 * Remaining responsibilities:
 * - Read-only bale operations for API endpoints
 * - Last bale offset queries for view entities
 */
@Slf4j
@Component
@AllArgsConstructor
public class ReadBaleUseCase {

    private final BaleRepository baleRepository;
    private final ReadViewEntityUseCase readViewEntityUseCase;


    // Note: execute(List<ViewEntity>) method removed - no production usage found
    // All sync operations now handled by Spring Batch components

    public Integer executeLastBaleOfOffset(Long baleViewEntityId, Integer offset)
            throws NotFoundException, EntityErrorsException {
        ViewEntity baleViewEntity = readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        return baleRepository.findLastBaleWithOffset(baleViewEntity, offset);
    }

    public List<Bale> executeAllLastBaleOfOffset(Long baleViewEntityId, Integer offset)
            throws NotFoundException, EntityErrorsException {
        ViewEntity baleViewEntity = readViewEntityUseCase.execute(baleViewEntityId, ViewEntity.EntityType.BALE);
        return baleRepository.findAllLastBaleWithOffset(baleViewEntity, offset);
    }

    // Note: Sync methods removed - now handled by Spring Batch:
    // - executeByEntryNo() -> syncBradBaleUseCase.triggerBaleSync()
    // - executeWithEntryNoInBaleView() -> syncBradBaleUseCase.triggerBaleSyncForView()
    // - executeWithBaleViewIds() -> syncBradBaleUseCase.triggerBaleSyncForViews()

    // Note: fetchAllBales() method removed (67 lines) - complex sync logic now handled by Spring Batch components
}
